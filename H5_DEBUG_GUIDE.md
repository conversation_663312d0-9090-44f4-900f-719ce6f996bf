# H5 白屏问题排查指南

## 🔍 问题现象
- 微信开发者工具中页面正常显示
- H5 环境下出现白屏
- 控制台可能有错误信息

## 🛠️ 排查步骤

### 1. 检查浏览器控制台
打开 `http://localhost:8089/mobile/`，按 F12 打开开发者工具，查看：

#### Console 标签页
- 是否有 JavaScript 错误
- 是否有组件加载失败的错误
- 是否有网络请求失败

#### Network 标签页
- 检查静态资源是否加载成功
- 检查图片、CSS、JS 文件是否 404

#### Elements 标签页
- 检查 DOM 结构是否正常生成
- 检查是否有样式问题

### 2. 测试基础功能
访问测试页面：`http://localhost:8089/mobile/#/pages/test/test`

这个页面会测试：
- 系统信息获取
- 状态管理
- 存储功能
- 导航功能

### 3. 常见问题及解决方案

#### 问题1: uni.getAccountInfoSync 错误
**现象**: `uni.getAccountInfoSync is not a function`
**解决**: 已修复，使用条件编译处理平台差异

#### 问题2: uv-image 组件问题
**现象**: 图片组件在 H5 下不显示
**解决**: 已替换为原生 image 标签

#### 问题3: page-meta 组件问题
**现象**: page-meta 在 H5 下不支持
**解决**: 已使用条件编译，仅在小程序环境使用

#### 问题4: 系统信息获取失败
**现象**: wx.getSystemInfoSync 在 H5 下报错
**解决**: 已添加平台兼容性处理

#### 问题5: 组件导入问题
**现象**: 某些组件在 H5 下无法正常导入
**解决**: 检查组件是否支持 H5 平台

### 4. 调试技巧

#### 启用详细日志
在 `vite.config.ts` 中添加：
```typescript
export default defineConfig({
  // ...其他配置
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: true,
  },
  build: {
    sourcemap: true
  }
})
```

#### 使用 vconsole 调试
在 H5 环境下添加移动端调试工具：
```html
<!-- 在 index.html 中添加 -->
<script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
<script>
  if (window.location.hostname === 'localhost') {
    new window.VConsole();
  }
</script>
```

#### 分步调试
1. 先注释掉复杂组件，只保留基础结构
2. 逐步添加组件，确定问题组件
3. 针对问题组件进行修复

### 5. 修复建议

#### 立即尝试的解决方案

1. **清除缓存重新启动**
```bash
# 停止当前服务
# 删除 node_modules 和 dist
rm -rf node_modules dist
npm install
npm run dev:h5
```

2. **检查路由配置**
确保 `pages.json` 中的路由配置正确

3. **检查组件兼容性**
将可能有问题的组件替换为基础组件：
- `uv-image` → `image`
- `page-meta` → 条件编译
- 自定义组件 → 基础 view

4. **检查样式问题**
某些 SCSS 语法在 H5 下可能不兼容

#### 渐进式修复步骤

1. **第一步：确保基础页面显示**
   - 访问测试页面：`/pages/test/test`
   - 确认基础功能正常

2. **第二步：简化首页**
   - 注释掉复杂组件
   - 只保留基础结构和样式

3. **第三步：逐步恢复功能**
   - 一个一个添加组件
   - 每次添加后测试是否正常

4. **第四步：修复具体问题**
   - 针对有问题的组件进行平台兼容性处理

### 6. 当前状态

✅ **已修复的问题**:
- uni.getAccountInfoSync 平台兼容性
- 系统信息获取兼容性
- uv-image 组件替换
- page-meta 条件编译
- tabbar 组件图片显示

🔄 **需要验证的功能**:
- 首页基础显示
- 组件渲染
- 样式加载
- 交互功能

### 7. 快速验证

访问以下链接进行测试：

1. **测试页面**: `http://localhost:8089/mobile/#/pages/test/test`
2. **首页**: `http://localhost:8089/mobile/#/pages/index/index`

如果测试页面能正常显示，说明基础环境没问题，问题在于首页的某个组件。

### 8. 联系支持

如果问题仍然存在，请提供：
1. 浏览器控制台的完整错误信息
2. Network 标签页的请求状态
3. 测试页面的显示情况

这样可以更精确地定位问题所在。
