import { defineStore } from 'pinia'

export const useSystemStore = defineStore('system', {
    state: () => {
        return {
            systemInfo: {}, // 手机系统信息
            navBarHeight: 44, // 系统默认导航栏高度
            statusBarHeight: 0, // 系统默认状态栏高度
            safeAreaBottom: 0, // 底部安全区域距离
            windowWidth: 320, // 手机屏幕宽度
            windowHeight: 568, // 手机屏幕高度
            menuButtonInfo: {
                top: 0,
                height: 0
            }, // 胶囊按钮
            titleBarHeight: 0
        }
    },
    getters: {
        navBarAndStatusBarHeight() {
            return `${+(this.statusBarHeight || 0) + (this.navBarHeight || 0)}px`
        }
    },
    actions: {
        initSystemInfo() {
            let systemInfo = {}
            let navBarHeight = 44
            let statusBarHeight = 0
            let safeAreaBottom = 0
            let menuButtonInfo = { top: 24, height: 32 }

            try {
                // 尝试获取系统信息，兼容不同平台
                // #ifdef MP-WEIXIN
                systemInfo = wx.getSystemInfoSync()
                // #endif

                // #ifndef MP-WEIXIN
                systemInfo = uni.getSystemInfoSync()
                // #endif

                console.log('系统信息', systemInfo)

                navBarHeight = systemInfo.platform === 'ios' ? 44 : 48
                statusBarHeight = systemInfo.statusBarHeight || 0
                safeAreaBottom = systemInfo.screenHeight - (systemInfo?.safeArea?.bottom || systemInfo.screenHeight) || 0

                // 获取胶囊按钮信息（仅小程序环境）
                // #ifdef MP-WEIXIN
                try {
                    const res = uni.getMenuButtonBoundingClientRect()
                    if (res && res.top) {
                        menuButtonInfo = res
                    }
                } catch (error) {
                    console.warn('获取胶囊按钮信息失败:', error)
                }
                // #endif

                // #ifdef H5
                // H5 环境下设置默认值
                statusBarHeight = 0
                navBarHeight = 44
                menuButtonInfo = { top: 0, height: 44 }
                // #endif

            } catch (error) {
                console.warn('获取系统信息失败:', error)
                // 使用默认值
                systemInfo = {
                    platform: 'unknown',
                    windowWidth: 375,
                    windowHeight: 667,
                    statusBarHeight: 0
                }
            }

            this.titleBarHeight = menuButtonInfo?.top + (menuButtonInfo?.height - statusBarHeight) / 2

            // 存储系统信息
            this.systemInfo = systemInfo
            this.navBarHeight = navBarHeight
            this.statusBarHeight = statusBarHeight
            this.safeAreaBottom = safeAreaBottom
            this.menuButtonInfo = menuButtonInfo
            this.windowWidth = systemInfo.windowWidth || 375
            this.windowHeight = systemInfo.windowHeight || 667
        }
    },
    persist: {
        storage: {
            getItem: uni.getStorageSync,
            setItem: uni.setStorageSync
        }
    }
})
