import { getUserCenter } from '@/api/user'
import { TOKEN_KEY } from '@/enums/cacheEnums'
import cache from '@/utils/cache'
import { defineStore } from 'pinia'

interface UserSate {
  userInfo: Record<string, any>
  token: string | null
  temToken: string | null
  tokenExpired: boolean
}
export const useUserStore = defineStore<any>('userStore', {
  // id: 'userStore',
  state: (): UserSate => ({
    userInfo: {
      sysUser: {
        avatar: '',
        nickname: '',
        username: '',
        phone: '',
        wxOpenid: ''
      }
    },
    token: cache.get(TOKEN_KEY) || null,
    temToken: null,
    tokenExpired: false
  }),
  getters: {
    isLogin: (state: any) => !!state.token
  },
  actions: {
    async getUser() {
      // 获取用户信息
      const { data } = await getUserCenter({})
      // @ts-ignore
      this.userInfo = {
        permissions: [],
        roles: [],
        selfAndChildTenantList: [],
        tenantInfos: [],
        sysUser: data?.sysUser || {}
      }
    },
    login(token: string) {
      // @ts-ignore
      this.token = token
      cache.set(TOKEN_KEY, token)
    },
    logout() {
      // @ts-ignore
      this.token = ''
      // @ts-ignore
      this.userInfo = {}
      // @ts-ignore
      this.tokenExpired = false
      cache.remove(TOKEN_KEY)
    },
    checkToken() {
      // 检查token是否过期的逻辑
      // 这里可以添加实际的token验证逻辑
      return Promise.resolve()
    }
  },
  // @ts-ignore
  persist: {
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync
    }
  }
})
