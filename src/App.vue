<script setup lang="ts">
import { onLaunch } from '@dcloudio/uni-app'
import { useAppStore } from './stores/app'
import { useUserStore } from './stores/user'

const appStore = useAppStore()
const { getUser } = useUserStore()
import { getToken } from '@/utils/auth'

onLaunch(async () => {
  // 获取APP配置
  // await appStore.getConfig()
  // // #ifdef H5
  // await getUser()
  // // #endif
  // // #ifndef H5
  if (getToken()) {
    await getUser()
  }
  // // #endif
})
</script>
<style lang="scss">
@import '@/uni_modules/uv-ui-tools/index.scss';
</style>
