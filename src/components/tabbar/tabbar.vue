<template>
  <uv-tabbar
    v-if="showTabbar >= 0"
    v-bind="tabbarStyle"
    :value="showTabbar"
    @change="tabChange"
  >
    <uv-tabbar-item
      v-for="tab in tabbarList"
      :text="tab.text"
      :key="tab.pagePath"
    >
      <template #active-icon>
        <image
          :src="tab.selectedIconPath"
          style="width: 24px; height: 24px;"
          mode="aspectFit"
        />
      </template>
      <template #inactive-icon>
        <image
          :src="tab.iconPath"
          style="width: 24px; height: 24px;"
          mode="aspectFit"
        />
      </template>
    </uv-tabbar-item>
  </uv-tabbar>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const tabbarList = computed<any[]>(() => {
  return [
    {
      iconPath:
        'https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/tabbar/tabbar-home-inactive.png',
      selectedIconPath:
        'https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/tabbar/tabbar-home-active.png',
      text: '首页',
      pagePath: '/pages/index/index',
    },
    {
      iconPath:
        'https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/tabbar/tabbar-todo-inactive.png',
      selectedIconPath:
        'https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/tabbar/tabbar-todo-active.png',
      text: '待办',
      pagePath: '/pages/todo/todo',
    },
    {
      iconPath:
        'https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/tabbar/tabbar-inquiry-inactive.png',
      selectedIconPath:
        'https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/tabbar/tabbar-inquiry-active.png',
      text: '青贮询价',
      pagePath: '/pages/inquiry/inquiry',
    },
    {
      iconPath:
        'https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/tabbar/tabbar-user-inactive.png',
      selectedIconPath:
        'https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/tabbar/tabbar-user-active.png',
      text: '我的',
      pagePath: '/pages/user/user',
    },
  ]
})

const showTabbar = computed(() => {
  const currentPages = getCurrentPages()
  const currentPage = currentPages[currentPages.length - 1]
  const current = tabbarList.value.findIndex((item: any) => {
    return item.pagePath === '/' + currentPage.route
  })
  return current
})
const tabbarStyle = computed(() => ({
  activeColor: '#1677FF',
  inactiveColor: '#7E8694',
}))

const tabChange = (index: number) => {
  uni.switchTab({ url: tabbarList.value[index].pagePath })
}
</script>
