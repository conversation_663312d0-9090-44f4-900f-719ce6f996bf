<template>
  <page-meta
    class="coupon-page-meta"
    background-color="#F2F3F5"
    :page-style="pageStyle"
  >
    <view class="coupon-wrapper">
      <pro-token-expired v-if="tokenExpired" />
      <block v-else>
        <!-- <view class="header">
          <view
            class="header-right"
            @tap.stop="searchShow"
          >
            <image
              class="header-icon"
              src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/mini-apps/dcrg/order/filter-new.png"
            ></image>
            <text>筛选</text>
          </view>
        </view> -->
        <view class="coupon-wrapper--floatheader">
          <view
            class="coupon-wrapper--space"
            :style="{ height: (systemStore?.menuButtonInfo?.top || 24) + 'px' }"
          ></view>
          <view class="coupon-wrapper--header">
            <uv-image
              src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/ayn-logo-nav.svg"
              height="30"
              width="92"
            />
            <view class="coupon-wrapper--header__line"></view>
            <view class="coupon-wrapper--header__title">爱养牛招采平台</view>
          </view>
        </view>

        <!-- :style="{ paddingTop: (systemStore?.menuButtonInfo?.top || 24) + 44 + 'px' }" -->
        <view class="coupon-wrapper--list">
          <c-empty
            v-if="isEmpty && !loading"
            mode="data"
            text="暂无数据"
            style="height: 66vh"
          ></c-empty>
          <view v-if="!isEmpty && !loading">
            <view
              @tap.stop="toCouponHandler(item?.couponInstanceCode, true)"
              class="coupon-wrapper--list--container"
              v-for="item in list"
              :key="item.taskId"
            >
              <view class="coupon-wrapper--list--item">
                <view class="coupon-wrapper--list--item__header">
                  <view class="coupon-wrapper--list--item__header--left">
                    <view class="coupon-wrapper--list--item__header--left__label">
                      {{ item.couponName || '--' }}
                    </view>
                    <view class="coupon-wrapper--list--item__header--left__value">
                      <text
                        v-if="item.userName && item.userName !== '--'"
                        style="padding-right: 12rpx"
                      >
                        {{ item.userName }}
                      </text>
                      <text v-if="item.licensePlateNumber && item.licensePlateNumber !== '--'">
                        {{ item.licensePlateNumber }}
                      </text>
                    </view>
                  </view>
                  <view class="coupon-wrapper--list--item__header--mid">
                    <view class="top-circle"></view>
                    <view class="bottom-circle"></view>
                    <view class="line"></view>
                  </view>
                  <view class="coupon-wrapper--list--item__header--right">
                    <text class="unit">¥</text>
                    <text
                      class="price"
                      v-if="item.couponSourceType !== 'MERCHANT'"
                    >
                      {{ item.faceValue || '0' }}
                    </text>
                    <text
                      class="price"
                      v-else
                    >
                      -
                    </text>
                  </view>
                  <!-- <view
                                      v-show="item.couponSourceType === 'PLATFORM'"
                                      class="coupon-wrapper--list--item__header--mid"
                                  >
                                      <view class="top-circle"></view>
                                      <view class="bottom-circle"></view>
                                      <view class="line"></view>
                                  </view> -->
                  <!-- <view
                                      v-show="item.couponSourceType === 'PLATFORM'"
                                      class="coupon-wrapper--list--item__header--right"
                                  >
                                      <text class="unit">¥</text>
                                      <text class="price">{{ item.faceValue || '0' }}</text>
                                  </view> -->
                </view>
                <view class="coupon-wrapper--list--item__content">
                  <view class="coupon-wrapper--list--item__content__label">客户手机号</view>
                  <view class="coupon-wrapper--list--item__content__value">
                    {{ item.phone || '--' }}
                  </view>
                </view>
                <view class="coupon-wrapper--list--item__content">
                  <view class="coupon-wrapper--list--item__content__label">发放时间</view>
                  <view class="coupon-wrapper--list--item__content__value">
                    {{ item.createTime ? formatDate(item.createTime, 'YYYY-MM-DD HH:mm') : '--' }}
                  </view>
                </view>
                <view class="coupon-wrapper--list--item__content">
                  <view class="coupon-wrapper--list--item__content__label">状态</view>
                  <view class="coupon-wrapper--list--item__content__value">--</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
    <tabbar />
  </page-meta>
</template>

<script setup lang="js">
import { ref, computed } from 'vue'
import { useSystemStore } from "@/stores/system";
import { useUserStore } from "@/stores/user";
// import { queryCouponManage, couponDetail } from '@/api/coupon';
import { queryMineTask as queryCouponManage, queryMineTask as couponDetail } from '@/api/todo';
import { formatDate } from '@/utils/dateFormat'
import { useProList } from "@/hooks/useProList";
import {
onPullDownRefresh,
onReachBottom,
onShow,
onLoad,
} from "@dcloudio/uni-app";

const placeholderStyle = computed(() => {
  return `color: #7E8694; font-size: 32rpx`
})

const writeOff = ref({
  show: false,
  title: '输入核销码',
  // content: '输入核销码',
  confirmText: '确认核销',
  cancelText: '取消',
  showCancelButton: true,
  showConfirmButton: true
})


const searchPlanVisible = ref(false)
const searchPanelRef = ref()
const searchData = ref()
const writeOffCode = ref('')
const systemStore = useSystemStore();
const userStore = useUserStore();
const loading = ref(false);
const pageOptions = ref({})

const operationList = computed(() => {
  return [
      ...(userStore?.userInfo?.permissions || [])
  ]
})


const tokenExpired = computed(() => {
  return userStore?.tokenExpired
})

const filterForm = ref({
  keywords: null,
})
const extParams = computed(() => {
  if (userStore?.isPlatform) {
      return {
          ...filterForm.value,
          ...searchData.value
      }
  }
  return {
      ...filterForm.value,
      ...searchData.value,
      // status: 'NOT_USED'
  }
})


function toCouponHandler(couponInstanceCode, writeOffDisabled = false) {
  uni.navigateTo({
      url: `/pkg-coupon/coupon-manual/index?couponInstanceCode=${couponInstanceCode}&writeOffDisabled=${writeOffDisabled}`
  })
}

async function writeOffHandler() {
  if (!writeOffCode.value) {
      uni.showToast({
          title: '请输入核销码',
          icon: 'none',
      })
      return
  }
  try {
      const res = await couponDetail({ couponInstanceCode: writeOffCode.value })
      if (res?.data) {
          writeOffCloseHandler()
          toCouponHandler(writeOffCode.value)
      } else {
          uni.reLaunch({
              url: `/pkg-coupon/coupon-result/index?title=核销码有误`
          })
      }
  } catch (error) {
      console.log('获取卡券详情失败', error)
      uni.reLaunch({
          url: `/pkg-coupon/coupon-result/index?title=核销码有误`
      })
  }
}
function writeOffCancelHandler() {
  writeOff.value.show = false
}
function writeOffCloseHandler() {
  writeOff.value.show = false
}


function manualHandler() {
  writeOffCode.value = ''
  writeOff.value.show = true
}

const { list, refresh, isEmpty, remoteMethod } = useProList({
apiFn: async(params) => {
  uni.showLoading({
    title: '加载中...'
  })
  const res = await queryCouponManage(params)
  uni.hideLoading()
  return res
},
responseHandler: (list) => {
  return list;
},
extParams: extParams,
lazyLoad: true,
addPageEvent: false,
});


const pageStyle = computed(() => {
  return searchPlanVisible.value ? 'overflow: hidden' : ''
})
const resetSearch = () => {
  searchData.value = null
  searchPlanVisible.value = false
  refresh()
}

const closeSearch = () => {
  searchPlanVisible.value = false
}

const selectSearch = (data) => {
  searchPlanVisible.value = false
  searchData.value = data
  refresh()
}

/** 打开搜索查询 */
const searchShow = () => {
  searchPlanVisible.value = true
  if (searchPanelRef.value) {
      searchPanelRef.value.open()
  }
}


function changeSearchHandler(value) {
  filterForm.value.keywords = value || null
  refresh()
}

function keywordSearchHandler(value) {
  filterForm.value.keywords = value || null
  refresh()
}

onShow(async () => {
  // await userStore.checkToken();
  if (!tokenExpired.value) {
      loading.value = true;
      await refresh()
      loading.value = false;
  }
})
onLoad((options) => {
  pageOptions.value = options
})

onPullDownRefresh(() => {
console.log("onPullDownRefresh");
if (!tokenExpired.value) {
  refresh();
}
});

onReachBottom(() => {
console.log("onReachBottom");
if (!tokenExpired.value) {
  remoteMethod();
}
});
</script>

<style lang="scss">
.coupon-wrapper {
  // overflow: hidden;
  // display: flex;
  // flex-direction: column;
  // height: 100vh;
  background-image: url('https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/home-bg.png');
  background-position: left top;
  background-repeat: no-repeat;
  background-size: 100% 47vh;
  background-color: #f0f2f5;
  box-sizing: border-box;
  min-height: 100vh;

  .header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 20rpx 32rpx 32rpx;
    .header-icon {
      width: 16px;
      height: 16px;
      margin-right: 8rpx;
      vertical-align: middle;
    }
    .header-right {
      display: flex;
      align-items: center;
      width: max-content;
      color: var(--Neutral-secondary, #1c2026);
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 28rpx;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
  }

  &--floatheader {
    position: fixed;
    z-index: 20;
    background-color: transparent;
    width: 100%;
    left: 0;
    // top: 96rpx;
  }
  &--header {
    display: flex;
    box-sizing: content-box;
    align-items: center;
    overflow: hidden;
    height: 60rpx;
    // box-sizing: border-box;
    padding-bottom: 24rpx;
    padding-left: 24rpx;

    &__line {
      width: 1px;
      height: 32rpx;
      background-color: rgba(0, 0, 0, 0.2);
      margin: 0 24rpx;
      color: var(--text-icon-font-gy-190, rgba(0, 0, 0, 0.9));
      font-family: 'PingFang SC';
      font-size: 36rpx;
      font-style: normal;
      font-weight: 500;
    }

    &__title {
      color: var(--Neutral-primary, #1c2026);
      font-family: 'PingFang SC';
      font-size: 36rpx;
      font-style: normal;
      font-weight: 600;
      flex-shrink: 0;
    }

    &__input {
      position: relative;
      flex-shrink: 0;
      flex: 1;

      :deep(.c-easyinput__content-input) {
        height: 64rpx !important;
        padding-left: 48rpx !important;
      }

      &--icon {
        position: absolute;
        left: 24rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 24rpx;
        height: 24rpx;
        z-index: 1;
      }
    }
  }

  &--list {
    padding: 0 32rpx;
    // padding-bottom: calc(constant(var(safe-area-inset-bottom, 0) + 100rpx));
    // padding-bottom: calc(var(safe-area-inset-bottom, 0) + 100rpx);
    // flex: 1;
    // overflow: auto;
    box-sizing: border-box;
    // height: 400px;

    &--container {
      display: flex;
      align-items: center;
    }

    &--icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
    }

    &--item {
      background-color: #fff;
      border-radius: 20rpx;
      background: #fff;
      padding: 32rpx;
      margin-bottom: 24rpx;
      flex: 1;

      &__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 148rpx;
        border-radius: 16rpx;
        background: #eef6ff;
        margin-bottom: 24rpx;
        // border: 2rpx solid #d5e9ef;
        &--left {
          flex: 1;
          padding-left: 32rpx;
          box-sizing: border-box;
          border-right: none;
          &__label {
            color: #1c2026;
            text-align: left;
            font-family: 'PingFang SC';
            font-size: 32rpx;
            font-style: normal;
            font-weight: 600;
            line-height: 24px;
          }
          &__value {
            color: #505762;
            text-align: left;
            font-family: 'PingFang SC';
            font-style: normal;
            font-size: 28rpx;
            font-weight: 400;
            line-height: 22px;
          }
        }
        &--mid {
          width: 16rpx;
          flex-shrink: 0;
          height: calc(100% + 4rpx);
          position: relative;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          top: 0;
          z-index: 10;
          left: 8rpx;
          .bottom-circle,
          .top-circle {
            width: 16rpx;
            height: 16rpx;
            position: absolute;
            left: 0;
            border-radius: 50%;
            background-color: #fff;
            border: 2rpx solid #fff;
            z-index: 1;
          }
          .bottom-circle {
            bottom: -8rpx;
          }
          .top-circle {
            top: -8rpx;
          }
          .line {
            width: 2rpx;
            height: 100%;
            background-color: transparent;
            border: 2rpx dashed #fff;
          }
        }
        &--right {
          box-sizing: border-box;
          height: 100%;
          width: 200rpx;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          text-align: center;
          font-family: Avenir;
          font-style: normal;
          border-radius: 0px 16rpx 16rpx 0px;
          background: linear-gradient(180deg, #5890ff 0%, #3879f8 100%);
          // background: linear-gradient(180deg, #d9f9ff 0%, #d0eff9 100%);
          .unit {
            font-size: 24rpx;
            font-weight: 500;
            padding-right: 8rpx;
          }
          .price {
            font-size: 52rpx;
            font-weight: 800;
          }
        }
        &__label {
          color: var(--Neutral-primary, #1c2026);
          text-align: center;
          /* Medium/T5-16 */
          font-family: 'PingFang SC';
          font-size: 32rpx;
          font-style: normal;
          font-weight: 600;
        }
      }

      &__content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 44rpx;
        font-family: 'PingFang SC';
        font-size: 28rpx;
        font-style: normal;
        font-weight: 400;
        margin-top: 8rpx;

        &:first-child {
          margin-top: 16rpx;
        }

        &__label {
          color: var(--Neutral-secondary, #7e8694);
          width: 156rpx;
          flex-shrink: 0;
        }

        &__value {
          color: var(--Neutral-primary, #1c2026);
          text-align: left;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          word-break: break-all;
        }
      }
    }
  }
}

.coupon-float-button-wrapper {
  position: fixed;
  bottom: 140px;
  right: 12px;
  background-color: transparent;
  z-index: 1;
  &--button {
    width: 128rpx;
    height: 100rpx;
    background-color: #transparent;
    margin-top: 40rpx;
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    overflow: hidden;
    box-sizing: border-box;
    &__icon {
      width: 80rpx;
      height: 80rpx;
    }
  }
  &--text {
    position: absolute;
    left: 0;
    bottom: 0;
    border-radius: 20px;
    background: #fff;
    padding: 4px 6px;
    color: var(--Neutral-primary, #1c2026);
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 24rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 14px;
    box-sizing: border-box;
    &.scan {
      border: 2rpx solid #fff;
      background: #e0eaff;
      backdrop-filter: blur(10px);
      color: #1e63e7;
    }
    &.manual {
      border: 2rpx solid #fff;
      background: #e0ffff;
      backdrop-filter: blur(10px);
      color: #0ca4a4;
    }
  }
}

.common-dialog-popup-wrapper {
  border-radius: 10px;
  background: var(--background-grey-white, #fff);
  width: 640rpx;
  overflow: hidden;
  .common-dialog-popup-container {
    padding: 48rpx;
    .common-dialog-popup-header {
      margin-bottom: 32rpx;
      &--title {
        color: var(--text-grey-Primary, #1c2026);
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px; /* 144.444% */
      }
    }
    .common-dialog-popup-content {
      border-radius: 6px;
      background: var(--background-grey-lighter, #f6f7f9);
      padding: 24rpx 32rpx;
      box-sizing: border-box;
      height: 96rpx;
      width: 100%;
      &--input {
        background-color: transparent;
        height: 100%;
        box-sizing: border-box;
        text-align: left;
      }
    }
  }
  .common-dialog-popup-footer {
    height: 112rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 0.5px solid var(--border-grey-lighter, #e6eaf0);
    &--button {
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 32rpx;
      font-style: normal;
      font-weight: 400;
      border-right: 0.5px solid var(--border-grey-lighter, #e6eaf0);
      &:last-child {
        border-right: none;
      }
      .cancel {
        font-weight: 400;
        color: var(--text-grey-Regular, #505762);
      }
      &.confirm {
        color: var(--brand, #1b2420);
        font-weight: 600;
      }
    }
  }
}
</style>
