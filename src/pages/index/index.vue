<template>
  <page-meta
    class="home-page-meta"
    background-color="#F0F2F5"
    :page-style="pageStyle"
  >
    <view class="home-wrapper">
      <pro-token-expired v-if="tokenExpired" />
      <block v-else>
        <!-- 背景渐变 -->
        <view class="home-bg"></view>

        <!-- 导航栏 -->
        <view class="home-navbar">
          <view
            class="home-navbar-space"
            :style="{ height: (systemStore?.menuButtonInfo?.top || 24) + 'px' }"
          ></view>
          <view class="home-navbar-content">
            <view class="home-navbar-left">
              <uv-image
                src="https://prod-ylzapp-public.oss-cn-zhangjiakou.aliyuncs.com/frontend/fe-procurement-platform-uni/ayn-logo-nav.svg"
                height="30"
                width="92"
              />
              <view class="home-navbar-line"></view>
              <text class="home-navbar-title">爱养牛招采平台</text>
            </view>
          </view>
        </view>

        <!-- 搜索框 -->
        <view class="home-search">
          <view class="home-search-input">
            <image class="home-search-icon" src="/static/images/search-icon.svg" />
            <input
              class="home-search-text"
              placeholder="请输入关键词搜索"
              placeholder-style="color: rgba(0, 0, 0, 0.45); font-size: 16px;"
              v-model="searchKeyword"
              @confirm="handleSearch"
            />
            <text class="home-search-btn" @tap="handleSearch">搜索</text>
          </view>
        </view>

        <!-- Banner -->
        <view class="home-banner">
          <home-banner />
        </view>

        <!-- 功能图标区 -->
        <view class="home-icons">
          <view class="home-icon-item" @tap="navigateTo('/pages/procurement-plan/index')">
            <view class="home-icon-bg">
              <view class="home-icon-group procurement-plan">
                <image class="procurement-plan-icon1" src="/static/images/procurement-plan-icon1.svg" />
                <image class="procurement-plan-icon2" src="/static/images/procurement-plan-icon2.svg" />
                <image class="procurement-plan-icon3" src="/static/images/procurement-plan-icon3.svg" />
              </view>
            </view>
            <text class="home-icon-text">采购计划</text>
          </view>
          <view class="home-icon-item" @tap="navigateTo('/pages/procurement-project/index')">
            <view class="home-icon-bg">
              <view class="home-icon-group procurement-project">
                <image class="procurement-project-icon1" src="/static/images/procurement-project-icon1.svg" />
                <image class="procurement-project-icon2" src="/static/images/procurement-project-icon2.svg" />
              </view>
            </view>
            <text class="home-icon-text">采购立项</text>
          </view>
          <view class="home-icon-item" @tap="navigateTo('/pages/silage-inquiry/index')">
            <view class="home-icon-bg">
              <image class="home-icon silage-inquiry" src="/static/images/silage-inquiry-icon.svg" />
            </view>
            <text class="home-icon-text">青贮询价</text>
          </view>
        </view>

        <!-- 标签页 -->
        <view class="home-tabs">
          <view
            class="home-tab-item"
            :class="{ active: activeTab === 'announcement' }"
            @tap="switchTab('announcement')"
          >
            公告/邀请
          </view>
          <view
            class="home-tab-item"
            :class="{ active: activeTab === 'change' }"
            @tap="switchTab('change')"
          >
            变更公告
          </view>
          <view
            class="home-tab-item"
            :class="{ active: activeTab === 'result' }"
            @tap="switchTab('result')"
          >
            中标公示
          </view>
          <view
            class="home-tab-item"
            :class="{ active: activeTab === 'notice' }"
            @tap="switchTab('notice')"
          >
            中标公告
          </view>
          <view class="home-tab-indicator" :style="tabIndicatorStyle"></view>
        </view>

        <!-- 公告列表 -->
        <view class="home-announcements">
          <pro-empty
            v-if="isEmpty && !loading"
            scene="data"
            text="暂无数据"
            style="height: 50vh"
          ></pro-empty>
          <view v-else>
            <!-- 询价公告 -->
            <view class="announcement-card" @tap="navigateToDetail('inquiry', 1)">
              <view class="announcement-header">
                <view class="announcement-title-row">
                  <view class="announcement-tag inquiry">询价公告</view>
                  <text class="announcement-title">2025年度豆粕物料采购项目招标公</text>
                </view>
              </view>
              <text class="announcement-content">2025年现代牧业要采购豆粕，采购详情内容根据表单中的内容进。</text>
              <view class="announcement-footer">
                <view class="announcement-company">现代牧业采购部</view>
                <text class="announcement-time">2025/7/12 12:22</text>
              </view>
            </view>

            <!-- 招标公告 -->
            <view class="announcement-card" @tap="navigateToDetail('tender', 2)">
              <view class="announcement-header">
                <view class="announcement-title-row">
                  <view class="announcement-tag tender">招标公告</view>
                  <text class="announcement-title">2025年度豆粕物料采购项目招标公</text>
                </view>
              </view>
              <text class="announcement-content">2025年现代牧业要采购豆粕，采购详情内容根据表单中的内容进。</text>
              <view class="announcement-footer">
                <view class="announcement-company">现代牧业采购部</view>
                <text class="announcement-time">2025/7/12 12:22</text>
              </view>
            </view>

            <!-- 竞谈公告 -->
            <view class="announcement-card" @tap="navigateToDetail('negotiation', 3)">
              <view class="announcement-header">
                <view class="announcement-title-row">
                  <view class="announcement-tag negotiation">竞谈公告</view>
                  <text class="announcement-title">2025年度豆粕物料采购项目招标公</text>
                </view>
              </view>
              <text class="announcement-content">2025年现代牧业要采购豆粕，采购详情内容根据表单中的内容进。</text>
              <view class="announcement-footer">
                <view class="announcement-company">现代牧业采购部</view>
                <text class="announcement-time">发布于 2025/7/12 12:22</text>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
    <tabbar />
  </page-meta>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useSystemStore } from "@/stores/system";
import { useUserStore } from "@/stores/user";
import HomeBanner from '@/components/home-banner/index.vue'
import {
  onPullDownRefresh,
  onReachBottom,
  onShow,
  onLoad,
} from "@dcloudio/uni-app";

// 状态管理
const systemStore = useSystemStore();
const userStore = useUserStore();
const loading = ref(false);
const pageOptions = ref({})

// 搜索相关
const searchKeyword = ref('')

// 标签页相关
const activeTab = ref('announcement')

// 计算属性
const tokenExpired = computed(() => {
  return userStore?.tokenExpired
})

const pageStyle = computed(() => {
  return ''
})

const isEmpty = computed(() => {
  return false // 暂时设为false，显示示例数据
})

// 标签页指示器样式
const tabIndicatorStyle = computed(() => {
  const tabIndex = ['announcement', 'change', 'result', 'notice'].indexOf(activeTab.value)
  const left = tabIndex * 25 // 每个标签占25%宽度
  return {
    left: `${left}%`,
    width: '24px'
  }
})

// 方法
function handleSearch() {
  if (searchKeyword.value.trim()) {
    console.log('搜索关键词:', searchKeyword.value)
    // 这里可以调用搜索API
  }
}

function switchTab(tab: string) {
  activeTab.value = tab
  console.log('切换到标签页:', tab)
  // 这里可以根据标签页加载不同的数据
}

function navigateTo(url: string) {
  uni.navigateTo({
    url
  })
}

function navigateToDetail(type: string, id: number) {
  uni.navigateTo({
    url: `/pages/announcement-detail/index?type=${type}&id=${id}`
  })
}

// 生命周期
onShow(async () => {
  if (!tokenExpired.value) {
    loading.value = true;
    // 这里可以加载首页数据
    loading.value = false;
  }
})

onLoad((options) => {
  pageOptions.value = options
})

onPullDownRefresh(() => {
  console.log("onPullDownRefresh");
  if (!tokenExpired.value) {
    // 刷新数据
    uni.stopPullDownRefresh()
  }
});

onReachBottom(() => {
  console.log("onReachBottom");
  if (!tokenExpired.value) {
    // 加载更多数据
  }
});
</script>

<style lang="scss">
.home-wrapper {
  background-color: #F0F2F5;
  min-height: 100vh;
  position: relative;
}

.home-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 461px;
  background: linear-gradient(180deg, #A0C5FF 0%, rgba(160, 197, 255, 0) 100%);
  z-index: 1;
}

// 导航栏
.home-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: transparent;

  &-space {
    width: 100%;
  }

  &-content {
    padding: 0 12px 12px;
  }

  &-left {
    display: flex;
    align-items: center;
    height: 30px;
  }

  &-line {
    width: 0.5px;
    height: 16px;
    background-color: rgba(0, 0, 0, 0.2);
    margin: 0 12px;
  }

  &-title {
    color: #1C2026;
    font-family: 'PingFang SC';
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
  }
}

// 搜索框
.home-search {
  position: relative;
  z-index: 10;
  padding: 0 12px;
  margin-top: 102px;

  &-input {
    display: flex;
    align-items: center;
    height: 44px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 22px;
    padding: 0 18px;
    gap: 12px;
  }

  &-icon {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }

  &-text {
    flex: 1;
    height: 24px;
    font-size: 16px;
    color: #1D2129;
    background: transparent;
    border: none;
    outline: none;
  }

  &-btn {
    color: #0069FF;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
  }
}

// Banner
.home-banner {
  position: relative;
  z-index: 10;
  padding: 12px;
  margin-top: 12px;
}

// 功能图标区
.home-icons {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  padding: 10px 12px;
  margin: 12px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.5) 100%), #FFFFFF;
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.6) 100%) 1;
  border-radius: 10px;
  gap: 24px;
}

.home-icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.home-icon-bg {
  width: 32px;
  height: 32px;
  background: #FFFFFF;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.home-icon-group {
  position: relative;
  width: 28.8px;
  height: 28.84px;

  &.procurement-plan {
    .procurement-plan-icon1 {
      position: absolute;
      top: 0;
      left: 6.68px;
      width: 15.43px;
      height: 5.97px;
    }

    .procurement-plan-icon2 {
      position: absolute;
      top: 5.9px;
      left: 0;
      width: 28.8px;
      height: 22.94px;
    }

    .procurement-plan-icon3 {
      position: absolute;
      top: 13.67px;
      left: 6.06px;
      width: 17.08px;
      height: 7.29px;
    }
  }

  &.procurement-project {
    .procurement-project-icon1 {
      position: absolute;
      top: 0;
      left: 0;
      width: 22.75px;
      height: 28px;
    }

    .procurement-project-icon2 {
      position: absolute;
      top: 9.08px;
      left: 3.91px;
      width: 14.22px;
      height: 7.66px;
    }
  }
}

.home-icon {
  &.silage-inquiry {
    width: 25.93px;
    height: 25.94px;
  }
}

.home-icon-text {
  color: #1D2129;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  text-align: center;
}

// 标签页
.home-tabs {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.6) 100%);
  border-radius: 16px 16px 0 0;
  margin-top: 12px;
  gap: 16px;
}

.home-tab-item {
  color: #505762;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  text-align: center;
  flex: 1;
  position: relative;

  &.active {
    color: #1C2026;
    font-weight: 500;
  }
}

.home-tab-indicator {
  position: absolute;
  bottom: 0;
  height: 4px;
  background: #1677FF;
  border-radius: 2px 2px 0 0;
  transition: left 0.3s ease;
}

// 公告列表
.home-announcements {
  position: relative;
  z-index: 10;
  padding: 0 12px 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.announcement-card {
  background: #FFFFFF;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.announcement-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.announcement-title-row {
  display: flex;
  align-items: center;
  gap: 4px;
}

.announcement-tag {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 4px;
  height: 20px;
  border-radius: 2px;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  white-space: nowrap;

  &.inquiry {
    background: #F0EDF8;
    color: #563F96;
  }

  &.tender {
    background: #FEEAF5;
    color: #C4277E;
  }

  &.negotiation {
    background: #E8F3F5;
    color: #126A7A;
  }
}

.announcement-title {
  color: #1D2129;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  flex: 1;
}

.announcement-content {
  color: #4E5969;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.announcement-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.announcement-company {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 4px;
  background: #F5F7FA;
  border-radius: 2px;
  color: #4E5969;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

.announcement-time {
  color: #86909C;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
}

</style>
