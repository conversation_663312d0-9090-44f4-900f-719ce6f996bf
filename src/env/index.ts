import { BASE_URL as PROD_BASE_URL } from '@/env/prod'
import { BASE_URL as DEV_BASE_URL } from '@/env/dev'
import { BASE_URL as TRIAL_BASE_URL } from '@/env/trial'

const env = uni?.getAccountInfoSync()?.miniProgram?.envVersion
const baseEnvUrl = env === 'develop' ? DEV_BASE_URL : env === 'trial' ? TRIAL_BASE_URL : PROD_BASE_URL
console.log('当前小程序环境', env)
console.log('当前小程序环境API地址', baseEnvUrl.api_url)

const apiOptions: any = {
  baseUrl: baseEnvUrl.api_url
}

export default apiOptions
